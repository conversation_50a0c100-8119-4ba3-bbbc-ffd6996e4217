import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>ton,
  Ty<PERSON>graphy,
  Box,
  IconButton,
  AppBar,
  Tool<PERSON>,
  List,
  ListItem,
  ListItemText,
  Divider,
  useMediaQuery,
  useTheme,
  ListItemButton
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { Formik, Form } from "formik";
import * as yup from "yup";

interface Service {
  id: string;
  name: string;
  description: string;
}

interface Category {
  name: string;
  isPrimary: boolean;
  services: Service[];
}

interface AddBusinessCategoryModalProps {
  open: boolean;
  onClose: () => void;
  onAddCategory: (categoryName: string) => void;
}

const AddBusinessCategoryModal: React.FC<AddBusinessCategoryModalProps> = ({
  open,
  onClose,
  onAddCategory,
}) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));
  
  // Sample data for the services view
  const categories = [
    {
      name: "Eye Care Clinic",
      isPrimary: true,
      services: [
        {
          id: "1",
          name: "Cataract Eye Treatment",
          description: "At Sri Eye Care, we offer advanced cataract eye treatment. Our ophthalmologists use advanced techniques for cataract surgery. We take pride in providing the best cataract treatment."
        },
        {
          id: "2",
          name: "Lasik Treatment",
          description: "Lasik treatment is one of the most common laser treatments. The laser eye surgery would reshape the cornea to improve vision."
        },
        {
          id: "3",
          name: "Phakic Intraocular Lens",
          description: "At Sri Eye Care, we use advanced technology for Lasik eye surgery and Lasik treatment for the best results."
        },
        {
          id: "4",
          name: "Retina Treatment",
          description: "We provide treatment options for retinal-related concerns such as diabetic retinopathy and retinal detachment."
        },
        {
          id: "5",
          name: "Glaucoma Treatment",
          description: "At Sri Eye Care, we offer various treatment options for glaucoma, such as medicines, laser treatment, and surgery."
        },
        {
          id: "6",
          name: "Orbit and Oculoplasty Treatment",
          description: "Orbit and oculoplasty surgery give a refreshed and rejuvenated appearance. At Sri Eye Care, we offer the best treatment."
        },
        {
          id: "7",
          name: "Paediatric Treatment",
          description: "At Sri Eye Care, we are equipped with a team of experienced pediatric ophthalmologists who specialize in treating children's eye conditions."
        }
      ]
    }
  ];

  // Validation schema
  const categorySchema = yup.object({
    categoryName: yup
      .string()
      .required("Business category is required")
      .min(3, "Category name must be at least 3 characters")
      .max(50, "Category name must be at most 50 characters")
  });

  // Initial values
  const initialValues = {
    categoryName: ""
  };

  const handleSubmit = (values: { categoryName: string }, { resetForm }: any) => {
    onAddCategory(values.categoryName);
    resetForm();
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      fullScreen={fullScreen}
      PaperProps={{
        style: {
          backgroundColor: "white",
          borderRadius: "8px",
          maxHeight: "90vh"
        }
      }}
    >
      <AppBar 
        position="relative" 
        color="transparent" 
        elevation={0}
        sx={{ 
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
          bgcolor: "white"
        }}
      >
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={onClose}
            aria-label="back"
            sx={{ color: "black" }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" }
            }}
          >
            Services
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            aria-label="more"
            sx={{ color: "black", mr: 1 }}
          >
            <MoreVertIcon />
          </IconButton>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            aria-label="close"
            sx={{ color: "black" }}
          >
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>
      
      <DialogContent 
        sx={{ 
          p: 0, 
          bgcolor: "white",
          "&:first-of-type": {
            pt: 0
          }
        }}
      >
        {categories.map((category, index) => (
          <Box key={index}>
            <Box sx={{ p: 2 }}>
              <Typography 
                variant="h6" 
                sx={{ 
                  color: "black",
                  fontSize: { xs: "1rem", sm: "1.1rem" }
                }}
              >
                {category.name}
              </Typography>
              <Typography 
                variant="caption" 
                sx={{ 
                  color: "rgba(0, 0, 0, 0.6)",
                  fontSize: { xs: "0.7rem", sm: "0.75rem" }
                }}
              >
                {category.isPrimary ? "Primary category" : "Additional category"}
              </Typography>
            </Box>
            
            <Divider />
            
            <List sx={{ p: 0 }}>
              {category.services.map((service, serviceIndex) => (
                <React.Fragment key={service.id}>
                  <ListItemButton 
                    sx={{ 
                      py: 2,
                      px: { xs: 2, sm: 3 }
                    }}
                  >
                    <ListItemText
                      primary={
                        <Typography 
                          variant="body1" 
                          sx={{ 
                            color: "black",
                            fontWeight: 500,
                            fontSize: { xs: "0.9rem", sm: "1rem" }
                          }}
                        >
                          {service.name}
                        </Typography>
                      }
                      secondary={
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            color: "rgba(0, 0, 0, 0.6)",
                            fontSize: { xs: "0.75rem", sm: "0.875rem" },
                            display: '-webkit-box',
                            overflow: 'hidden',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: 2,
                          }}
                        >
                          {service.description}
                        </Typography>
                      }
                      sx={{ mr: 2 }}
                    />
                    <ChevronRightIcon sx={{ color: "rgba(0, 0, 0, 0.54)" }} />
                  </ListItemButton>
                  {serviceIndex < category.services.length - 1 && (
                    <Divider component="li" />
                  )}
                </React.Fragment>
              ))}
            </List>
          </Box>
        ))}
      </DialogContent>
      
      <Box 
        sx={{ 
          mt: 'auto',
          p: 2, 
          borderTop: '1px solid rgba(0, 0, 0, 0.12)',
          display: 'flex',
          justifyContent: 'center'
        }}
      >
        <Formik
          initialValues={initialValues}
          validationSchema={categorySchema}
          onSubmit={handleSubmit}
        >
          {({ handleSubmit }) => (
            <Form onSubmit={handleSubmit} style={{ width: '100%' }}>
              <Button
                type="submit"
                variant="outlined"
                fullWidth
                sx={{
                  textTransform: "none",
                  color: "#1976d2",
                  borderColor: "#1976d2",
                  py: 1,
                  fontSize: { xs: "0.875rem", sm: "1rem" }
                }}
              >
                Add another business category
              </Button>
            </Form>
          )}
        </Formik>
      </Box>
    </Dialog>
  );
};

export default AddBusinessCategoryModal;