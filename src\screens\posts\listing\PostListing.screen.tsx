import { FunctionComponent, useState, useContext, useEffect } from "react";
import PageProps from "../../../models/PageProps.interface";
//Widgets
import Box from "@mui/material/Box";

import {
  Card,
  CardContent,
  Typography,
  Grid,
  CardMedia,
  FormHelperText,
} from "@mui/material";
import { Divider, Grid2 } from "@mui/material";
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";
import PostCard from "./postCard/postCard.screen";
import UpdatesSection from "../updatesSection/updatesSection.screen";
import {
  IGoogleCreatePostResponse,
  LocalPost,
} from "../../../interfaces/request/IGoogleCreatePostResponse";
import { useSelector } from "react-redux";
import { LoadingContext } from "../../../context/loading.context";
import PostsService from "../../../services/posts/posts.service";
import { Formik } from "formik";
import * as yup from "yup";
import {
  ILocationListRequestModel,
  IRetrievePostRequestModel,
} from "../../../interfaces/request/ILocationListRequestModel";
import {
  Drawer,
  FormControl,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../../interfaces/response/IBusinessGroupsResponseModel";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../../interfaces/response/IBusinessListResponseModel";
import BusinessService from "../../../services/business/business.service";
import { useDispatch } from "react-redux";
import OutlinedInput from "@mui/material/OutlinedInput";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../../interfaces/response/ILocationsListResponseModel";
import Button from "@mui/material/Button";
import { getIn } from "yup";
import { ToastContext } from "../../../context/toast.context";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import { MessageConstants } from "../../../constants/message.constant";
import PostAddOutlinedIcon from "@mui/icons-material/PostAddOutlined";

const PostsListing: FunctionComponent<PageProps> = ({ title }) => {
  const dispatch = useDispatch();
  const [posts, setPosts] = useState<LocalPost[]>([]);
  const { setLoading } = useContext(LoadingContext);
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const _postsService = new PostsService(dispatch);
  const INITIAL_VALUES: IRetrievePostRequestModel = {
    businessId: 0,
    businessGroupId: 0,
    locationId: 0,
  };
  const [businessGroupsOnBusiness, setBusinessGroupsOnBusiness] = useState<
    IBusinessGroup[]
  >([]);
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [initialValues, setInitialValues] =
    useState<IRetrievePostRequestModel>(INITIAL_VALUES);
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [locations, setLocations] = useState<ILocation[]>([]);
  const _businessService = new BusinessService(dispatch);
  const { setToastConfig, setOpen } = useContext(ToastContext);

  const fetchPosts = async (values: IRetrievePostRequestModel) => {
    const isValid = await LocationSchema.isValid(values);
    if (isValid) {
      try {
        setLoading(true);
        const postsReponse: IGoogleCreatePostResponse =
          await _postsService.retrievePosts(
            userInfo.id,
            values.businessGroupId,
            values.businessId,
            values.locationId
          );

        setPosts(postsReponse.data);
        if (postsReponse.data.length === 0) {
          setToastConfig(
            ToastSeverity.Info,
            MessageConstants.NoPostsFound,
            true
          );
        }
      } catch (error: any) {
        setToastConfig(ToastSeverity.Error, error.response.data.error, true);
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    getBusiness();
    getBusinessGroups();
    getLocationsList();
  }, []);

  const LocationSchema = yup.object().shape({
    businessId: yup
      .number()
      .moreThan(0, "Business must be selected") // Ensures value is greater than 0
      .required("Business is required"),
    businessGroupId: yup
      .number()
      .moreThan(0, "Account must be selected") // Ensures value is greater than 0
      .required("Account is required"),
    locationId: yup
      .number()
      .moreThan(0, "Location must be selected") // Ensures value is greater than 0
      .required("Location is required"),
  });

  const getBusiness = async () => {
    try {
      setLoading(true);
      let roles: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (roles.list.length > 0) {
        setBusinessList(roles.list);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getBusinessGroups = async () => {
    try {
      setLoading(true);
      let businessGroups: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (businessGroups.data.length > 0) {
        setBusinessGroups(businessGroups.data);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getLocationsList = async () => {
    try {
      setLoading(true);
      let locationsList: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      if (locationsList.list.length > 0) {
        setLocations(locationsList.list);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box className="bodyPart">
              <Box className="commonTableHeader">
                {/* <h3 className="commonTitle">
                  Create & Update Posts for 0 Verified & Connected listings
                </h3> */}
              </Box>
              {/* <Divider style={{ margin: 10, height: 5 }} /> */}
              <UpdatesSection />
              <Divider style={{ marginBottom: 30, height: 5 }} />
              <Formik
                enableReinitialize
                initialValues={{ ...initialValues }}
                validationSchema={LocationSchema}
                onSubmit={(values, { setSubmitting }) => {
                  fetchPosts(values);
                }}
              >
                {({
                  values,
                  errors,
                  touched,
                  handleChange,
                  handleBlur,
                  handleSubmit,
                  setFieldValue,
                  handleReset,
                  isSubmitting,
                  isValid,
                  /* and other goodies */
                }) => (
                  <form onSubmit={handleSubmit} onReset={handleReset}>
                    <Box sx={{ flexGrow: 1 }}>
                      <Grid container spacing={2} sx={{ padding: 2 }}>
                        <Grid item xs={12} sm={6} md={3}>
                          <FormControl
                            variant="outlined"
                            fullWidth
                            onBlur={handleBlur}
                            error={Boolean(
                              touched.businessId && errors.businessId
                            )}
                          >
                            <InputLabel id="outlined-country-dropdown-label">
                              Business
                            </InputLabel>

                            <Select
                              fullWidth
                              id="businessId"
                              label="Business"
                              value={values.businessId.toString()}
                              onChange={(evt: SelectChangeEvent) => {
                                setBusinessGroupsOnBusiness(
                                  businessGroups.filter(
                                    (x: IBusinessGroup) =>
                                      x.businessId === +evt.target.value
                                  )
                                );
                                setFieldValue("businessGroupId", 0);
                                setFieldValue("locationId", 0);
                                setFieldValue("businessId", +evt.target.value);
                              }}
                              sx={{
                                backgroundColor: "var(--whiteColor)",
                                borderRadius: "5px",
                              }}
                            >
                              <MenuItem value={0}>Select</MenuItem>
                              {businessList &&
                                businessList.map((business: IBusiness) => (
                                  <MenuItem
                                    key={business.id}
                                    value={business.id.toString()}
                                  >
                                    {business.businessName}
                                  </MenuItem>
                                ))}
                            </Select>
                            <FormHelperText>
                              {touched.businessId && errors.businessId
                                ? errors.businessId
                                : ""}
                            </FormHelperText>
                          </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <FormControl
                            variant="outlined"
                            fullWidth
                            error={Boolean(
                              touched.businessGroupId && errors.businessGroupId
                            )}
                            onBlur={handleBlur}
                          >
                            <InputLabel id="outlined-country-dropdown-label">
                              Account
                            </InputLabel>

                            <Select
                              fullWidth
                              id="businessGroupId"
                              label="Group"
                              value={values.businessGroupId.toString()}
                              onChange={(evt: SelectChangeEvent) => {
                                setFieldValue(
                                  "businessGroupId",
                                  +evt.target.value
                                );
                                setFieldValue("locationId", 0);
                              }}
                              sx={{
                                backgroundColor: "var(--whiteColor)",
                                borderRadius: "5px",
                              }}
                            >
                              <MenuItem value={0}>Select</MenuItem>
                              {businessGroupsOnBusiness &&
                                businessGroupsOnBusiness.map(
                                  (businessGroup: IBusinessGroup) => (
                                    <MenuItem
                                      key={businessGroup.id}
                                      value={businessGroup.id.toString()}
                                    >
                                      {businessGroup.accountName}
                                    </MenuItem>
                                  )
                                )}
                            </Select>
                            <FormHelperText>
                              {touched.businessGroupId && errors.businessGroupId
                                ? errors.businessGroupId
                                : ""}
                            </FormHelperText>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} sm={6} md={3}>
                          <Box className="commonSelect">
                            <FormControl
                              variant="outlined"
                              fullWidth
                              error={Boolean(
                                touched.locationId && errors.locationId
                              )}
                              onBlur={handleBlur}
                            >
                              <InputLabel>Locations</InputLabel>
                              <Select
                                fullWidth
                                id="locationId"
                                label="Location"
                                value={values.locationId.toString()}
                                onChange={(event: SelectChangeEvent) => {
                                  const {
                                    target: { value },
                                  } = event;
                                  setFieldValue("locationId", value);
                                }}
                                sx={{
                                  backgroundColor: "var(--whiteColor)",
                                  borderRadius: "5px",
                                }}
                              >
                                <MenuItem value={0}>Select</MenuItem>
                                {locations &&
                                  locations
                                    .filter((x: ILocation) => {
                                      const selectedBusiness =
                                        businessGroupsOnBusiness.filter(
                                          (bus) =>
                                            bus.id === values.businessGroupId
                                        );

                                      if (
                                        selectedBusiness &&
                                        selectedBusiness.length == 1
                                      ) {
                                        return (
                                          selectedBusiness[0].accountId ===
                                          x.gmbAccountId
                                        );
                                      } else {
                                        return false;
                                      }
                                    })
                                    .map((location: ILocation) => (
                                      <MenuItem
                                        key={location.id}
                                        value={location.id}
                                      >
                                        {location.gmbLocationName}
                                      </MenuItem>
                                    ))}
                              </Select>
                              <FormHelperText>
                                {touched.locationId && errors.locationId
                                  ? errors.locationId
                                  : ""}
                              </FormHelperText>
                            </FormControl>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Button
                            className="commonShapeBtn"
                            variant="contained"
                            fullWidth
                            sx={{
                              minHeight: "50px", // Set the desired height
                            }}
                            type="submit"
                          >
                            Apply
                          </Button>
                        </Grid>
                      </Grid>
                    </Box>
                    <Grid container spacing={2} sx={{ padding: 2 }}>
                      {posts.length > 0 ? (
                        posts.map((post, index) => (
                          <Grid
                            spacing={1}
                            item
                            xs={12}
                            sm={6}
                            md={3}
                            key={index}
                          >
                            <PostCard
                              post={post}
                              refreshData={() => fetchPosts(values)}
                            />
                          </Grid>
                        ))
                      ) : (
                        <Grid item xs={12}>
                          <Box
                            display="flex"
                            justifyContent="center"
                            alignItems="center"
                            flexDirection="row"
                            py={6}
                            gap={2}
                          >
                            <PostAddOutlinedIcon
                              style={{ fontSize: 50, color: "#ccc" }}
                            />
                            <Typography variant="h6" color="textSecondary">
                              No Posts Found
                            </Typography>
                          </Box>
                        </Grid>
                      )}
                    </Grid>
                  </form>
                )}
              </Formik>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>
    </div>
  );
};

export default PostsListing;
