import React, { useState } from "react";
import {
  Box,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Link,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckIcon from "@mui/icons-material/Check";
import { Formik, Form } from "formik";
import * as yup from "yup";

// Define the form values type
interface AmenitiesFormValues {
  genderNeutralToilets: string;
  hasToilet: string;
}

interface AmenitiesProps {
  onSave?: (values: AmenitiesFormValues) => void;
  initialValues?: AmenitiesFormValues;
}

const Amenities: React.FC<AmenitiesProps> = ({ 
  onSave,
  initialValues
}) => {
  // State for modal
  const [isOpen, setIsOpen] = useState(false);

  // Default initial values
  const defaultValues: AmenitiesFormValues = {
    genderNeutralToilets: "",
    hasToilet: "Yes" // Default value as shown in the screenshot
  };

  // Validation schema
  const amenitiesSchema = yup.object({
    genderNeutralToilets: yup.string().required("Please select an option"),
    hasToilet: yup.string().required("Please select an option")
  });

  // Handle form submission
  const handleSubmit = (values: AmenitiesFormValues) => {
    if (onSave) {
      onSave(values);
    }
    console.log("Amenities options saved:", values);
    setIsOpen(false);
  };

  // Amenities options
  const amenitiesOptions = [
    {
      id: "genderNeutralToilets",
      label: "Has gender-neutral toilets"
    },
    {
      id: "hasToilet",
      label: "Has toilet"
    }
  ];

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={() => setIsOpen(true)}
        sx={{ textTransform: "none" }}
      >
        Amenities
      </Button>

      <Dialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px"
          }
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px 24px",
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white"
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black"
            }}
          >
            Amenities
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={() => setIsOpen(false)}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 3, bgcolor: "white" }}>
          <Formik
            initialValues={initialValues || defaultValues}
            validationSchema={amenitiesSchema}
            onSubmit={handleSubmit}
          >
            {({ values, handleChange, handleSubmit, errors, touched }) => (
              <Form onSubmit={handleSubmit}>
                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.7)",
                      display: "inline"
                    }}
                  >
                    Let customers know more about your business by showing attributes on your Business Profile. 
                    These may appear publicly on Search, Maps and other Google services.{" "}
                  </Typography>
                  <Link
                    component="button"
                    type="button"
                    variant="body2"
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none"
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log("Learn more clicked");
                    }}
                  >
                    Learn more
                  </Link>
                </Box>

                <Box sx={{ mb: 4 }}>
                  {amenitiesOptions.map((option) => (
                    <Box key={option.id} sx={{ mb: 3 }}>
                      <Typography 
                        variant="subtitle1" 
                        sx={{ 
                          color: "black", 
                          mb: 1,
                          fontWeight: 500
                        }}
                      >
                        {option.label}
                      </Typography>
                      
                      <RadioGroup
                        name={option.id}
                        value={values[option.id as keyof AmenitiesFormValues]}
                        onChange={handleChange}
                        row
                      >
                        <FormControlLabel 
                          value="Yes" 
                          control={
                            <Radio 
                              sx={{
                                color: "#1976d2",
                                '&.Mui-checked': {
                                  color: "#1976d2",
                                },
                              }}
                              checkedIcon={
                                option.id === "hasToilet" && values[option.id] === "Yes" ? (
                                  <Box sx={{ position: 'relative', display: 'flex', alignItems: 'center' }}>
                                    <Radio 
                                      checked={true}
                                      sx={{
                                        color: "#1976d2",
                                        '&.Mui-checked': {
                                          color: "#1976d2",
                                        },
                                      }}
                                    />
                                    <CheckIcon 
                                      sx={{ 
                                        position: 'absolute',
                                        fontSize: '1rem',
                                        color: 'white',
                                        left: '50%',
                                        top: '50%',
                                        transform: 'translate(-50%, -50%)'
                                      }} 
                                    />
                                  </Box>
                                ) : undefined
                              }
                            />
                          } 
                          label={
                            <Typography sx={{ color: "black" }}>
                              Yes
                            </Typography>
                          }
                          sx={{ mr: 4 }}
                        />
                        <FormControlLabel 
                          value="No" 
                          control={
                            <Radio 
                              sx={{
                                color: "#1976d2",
                                '&.Mui-checked': {
                                  color: "#1976d2",
                                },
                              }}
                            />
                          } 
                          label={
                            <Typography sx={{ color: "black" }}>
                              No
                            </Typography>
                          }
                        />
                      </RadioGroup>
                      
                      {errors[option.id as keyof AmenitiesFormValues] && 
                       touched[option.id as keyof AmenitiesFormValues] && (
                        <Typography color="error" variant="caption">
                          {errors[option.id as keyof AmenitiesFormValues]}
                        </Typography>
                      )}
                    </Box>
                  ))}
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-start",
                    mt: 3,
                    gap: 2
                  }}
                >
                  <Button
                    variant="contained"
                    type="submit"
                    sx={{
                      textTransform: "none",
                      bgcolor: "#1976d2",
                      color: "white"
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setIsOpen(false)}
                    sx={{
                      textTransform: "none",
                      color: "#1976d2",
                      borderColor: "#e0e0e0",
                      bgcolor: "white"
                    }}
                  >
                    Cancel
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Amenities;