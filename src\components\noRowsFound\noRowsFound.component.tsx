import Avatar from "@mui/material/Avatar";
import { useEffect, useState } from "react";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TableRowsRoundedIcon from "@mui/icons-material/TableRowsRounded";

const NoRowsFound = () => (
  <TableRow>
    {/* Set Max Col Span */}
    <TableCell colSpan={12} align="center">
      <Box display="flex" flexDirection="column" alignItems="center" py={4}>
        <TableRowsRoundedIcon style={{ fontSize: 50, color: "#ccc" }} />
        <Typography variant="subtitle1" color="textSecondary" mt={1}>
          No Rows Found
        </Typography>
      </Box>
    </TableCell>
  </TableRow>
);

export default NoRowsFound;
